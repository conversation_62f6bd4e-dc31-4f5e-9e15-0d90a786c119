#pragma once

#include <memory>
#include <optional>

#include "node.h"

namespace lddk {

    class ParserStateData: public std::enable_shared_from_this<ParserStateData>{
        struct Private{};
    public:
        ParserStateData(Private) {}
        static std::shared_ptr<ParserStateData> create(){
            return std::make_shared<ParserStateData>(Private{});
        }
        std::shared_ptr<ParserStateData> parent;
        enum class State {
            Start, InExpression, InStatement, InBlock,
            InFunction, InClass, InInterface, InTrait,
            InEnum, InStruct, InModule, InFile
        } state = State::Start;
        Node currentNode;

    private:
        ParserStateData(const ParserStateData& other) = default;
        friend class Parser;
    };

    class Parser {
    public:
        Parser() {}
        ~Parser() {}
        std::shared_ptr<ParserStateData> state = ParserStateData::create();
    };
}