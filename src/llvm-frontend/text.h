#pragma once

#include <string>
#include <fstream>
#include "skiplist.h"
#include <uchar.h>
#include <unicode/ucnv.h>

#define TEXT_SEGMENT_SIZE 1024

struct TextNode {
    char32_t text[TEXT_SEGMENT_SIZE];
    int append(const char32_t* str, int pos, int len){
        int n = 0;
        while (n < len && pos + n < TEXT_SEGMENT_SIZE) {
            text[pos + n] = str[n];
            n++;
        }
        return n;
    }
};

using TextSegments = SkipList<TextNode, int, 2_KElem>;

class TextIterator {
public:
    TextIterator() {}
    TextIterator(TextSegments::iterator it, int offset)
        : it_(it)
        , offset_(offset)
    {
    }
    TextIterator& operator=(const TextIterator& other) {
        it_ = other.it_;
        offset_ = other.offset_;
        return *this;
    }
    ~TextIterator() {}

private:
    friend class Text;
    TextSegments::iterator it_;
    int offset_;
};

class Text {
public:
    Text() {}
    ~Text() {}

    TextIterator begin() {
        return TextIterator(text_.begin(), 0);
    }
    TextIterator end() {
        return TextIterator(text_.end(), 0);
    }
    TextIterator getIterator(int offset) {
        auto find_result = text_.findI(offset);
        return TextIterator(find_result.first, find_result.second);
    }

    static Text loadFromUtf8File(std::fstream& file)
    {
        Text text;
        std::string content((std::istreambuf_iterator<char>(file)),
            std::istreambuf_iterator<char>());
        char32_t* u32;
        int len = utf8ToChar32(content.c_str(), content.size(), &u32);
        text.append(u32, len - 1);
        free(u32);
        return text;
    }

    void append(const char32_t* str, int len)
    {
        if (len <= 0) return;
        TextSegments::iterator it = text_.end();
        if (it == text_.begin()) {
            text_.append(TextNode{}, 0);
            it = text_.end();
        }
        it--;
        int lastSegmentLen = it.index();
        if (lastSegmentLen < TEXT_SEGMENT_SIZE) {
            int n = TEXT_SEGMENT_SIZE - lastSegmentLen - len;
            it->append(str, lastSegmentLen, n);
            len -= n;
            if (len <= 0) return;
        }
        while (len > 0) {
            auto it1 = text_.append(TextNode{}, len);
            it1->append(str, 0, TEXT_SEGMENT_SIZE);
            len -= TEXT_SEGMENT_SIZE;
        }
    }

    void addText(TextIterator it, const char32_t* str, int len) {
        auto current_segment = TextSegments::iterator(it.it_.getPtr());
        if (current_segment == text_.end()) {
            append(str, len);
            return;
        }
        int current_segment_len = current_segment.index();
        int n = TEXT_SEGMENT_SIZE - current_segment_len - len;
        current_segment->append(str, current_segment_len, n);
        len -= n;
        if (len <= 0) return;
        while (len > 0) {
            auto it1 = text_.append(TextNode{}, len);
            it1->append(str, 0, TEXT_SEGMENT_SIZE);
            len -= TEXT_SEGMENT_SIZE;
        }
    }


    static int utf8ToChar32(const char *utf8str, int len, char32_t** u32str)
    {
        mbstate_t state = { 0 };
        const char* p = utf8str;

        if (*u32str) return -1;
        *u32str = (char32_t*)malloc(len * sizeof(char32_t) + 1); // +1 为终止符
        if (!*u32str) return -1;

        char32_t* dst = *u32str;
        p = utf8str;
        size_t rc;
        int remaining = len;
        while (remaining > 0 && (rc = mbrtoc32(dst, p, remaining, &state)) != 0) {
            if (rc == (size_t)-1) {
                // invalid input
                free(*u32str);
                return -1; // 转换失败
            }
            else if (rc == (size_t)-2) {
                // truncated input
                break;
            }
            else if (rc == (size_t)-3) {
                // no surrogate pairs in UTF-32
                free(*u32str);
                return -1; // 转换失败
            }
            dst += 1;
            p += rc;
            remaining -= rc;
        }
        *dst = U'\0'; // 终止符
        len = dst - *u32str + 1;
        *u32str = (char32_t*)realloc(*u32str, len * sizeof(char32_t));
        return len;
    }

    static int char32ToUtf8(const char32_t* u32str, int len, char** utf8str) {
        if (utf8str == nullptr || *utf8str) return -1;
        mbstate_t state = { 0 };
        const char32_t* p = u32str;

        size_t max_len = len * 4 + 1;

        char* utf8 = (char*)malloc(max_len);
        if (!utf8) return -1;

        char* dst = utf8;
        p = u32str;
        while (1) {
            size_t bytes = c32rtomb(dst, *p, &state);
            if (bytes == (size_t)-1) {
                free(utf8);
                return -1;
            }
            dst += bytes;

            if (*p == U'\0') break;
            p++;
        }
        *utf8str = (char*)realloc(utf8, dst - utf8);

        return dst - utf8;
    }


private:
    TextSegments text_;
};