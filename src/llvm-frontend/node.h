#pragma once

#include <string>
#include <any>
#include <map>
#include <array>
#include <vector>
#include <unordered_map>
#include <cstdint>
#include <cassert>
#include <mutex>
#include <optional>
#include <nlohmann/json.hpp>
#include <blake3.h>
#include <bit>


namespace lddk {

    static_assert(std::endian::native == std::endian::little, "Only little endian is supported");
    #define BLAKE3_HASH_SIZE 32
    using HashType = std::array<uint8_t, BLAKE3_HASH_SIZE>;
}
namespace std{
    template <>
    struct hash<lddk::HashType> {
        size_t operator()(const lddk::HashType &k) const {
            return *reinterpret_cast<const size_t *>(k.data());
        }
    };
}

namespace lddk {

class NodeStore;
class Node;

class Node{
public:
    Node() = default;
    ~Node(){
        if (data) {
            free(data);
        }
    }

    Node(const Node& other){
        hash=other.hash;
        type_hash=other.type_hash;
        data_size=other.data_size;
        data=malloc(data_size);
        memcpy(data, other.data, data_size);
        out_edges=other.out_edges;
    }

    Node(Node&& other){
        hash=other.hash;
        type_hash=other.type_hash;
        data_size=other.data_size;
        data=other.data;
        out_edges=std::move(other.out_edges);
        other.data=nullptr;
        other.data_size=0;
        other.hash.reset();
        other.type_hash.reset();
    }

    Node& operator=(const Node& other){
        if (this == &other) return *this;
        if (data) {
            free(data);
        }
        hash=other.hash;
        type_hash=other.type_hash;
        data_size=other.data_size;
        data=malloc(data_size);
        memcpy(data, other.data, data_size);
        out_edges=other.out_edges;
        return *this;
    }

    Node& operator=(Node&& other){
        if (this == &other) return *this;
        if (data) {
            free(data);
        }
        hash=other.hash;
        type_hash=other.type_hash;
        data_size=other.data_size;
        data=other.data;
        out_edges=std::move(other.out_edges);
        other.data=nullptr;
        other.data_size=0;
        other.hash.reset();
        other.type_hash.reset();
        return *this;
    }

	HashType finalize();
	bool isWriteable() const{return !hash.has_value();}
    bool updateData(void *new_data, size_t new_data_size){
        if(!isWriteable()) return false;
        if (data) {
            free(data);
        }
        data = new_data;
        data_size = new_data_size;
        return true;
    }
    void *getData() const { return data; }
    size_t getDataSize() const { return data_size; }

    bool addOutEdge(const HashType &key, const HashType &to)
    {
        if (!isWriteable()) return false;
        out_edges.insert({key, to});
        return true;
    }


	std::optional<HashType> getTypeHash() const { return type_hash; }
    std::optional<HashType> getHash() const { return hash; }

    bool isEqual(const Node& other, bool strict = false) const
    {
        if (!strict)
        {
            if (hash.has_value() && other.hash.has_value())
                return hash == other.hash;
            else return false;
        }else{
            if (type_hash != other.type_hash) return false;
            if (data_size != other.data_size) return false;
            return memcmp(data, other.data, data_size) == 0;
        }
        
    }

    nlohmann::json to_json() const;
	static Node from_json(const nlohmann::json &j);

	static std::string base64_encode(const void *data, size_t data_size);

	static void *base64_decode(const std::string &base64_str, size_t &data_size);

	static std::string base64_encode(const HashType &hash);
	static HashType    base64_decode(const std::string &base64_str);

private:
	static HashType blake3_hash(void           *data,
								size_t          data_size,
								const HashType &type_hash,
                                const std::multimap<HashType, HashType> &out_edges);

    void *data = nullptr;
    size_t data_size = 0;
    std::optional<HashType> hash, type_hash;
    std::multimap<HashType, HashType> out_edges; // key: key, value: to
    friend class NodeStore;
};

class NodeStore{
public:
    struct Edge{
        HashType from, to, value;
    };
	static NodeStore &instance();
	const Node *commitObject(Node &node);
	const Node *retrieve(const HashType &hash) const;

    std::vector<Edge> getInEdges(const HashType &hash) const
    {
        std::vector<Edge> edges;
        auto range = in_edges.equal_range(hash);
        for (auto it = range.first; it != range.second; ++it)
        {
            edges.push_back(it->second);
        }
        return edges;
    }

    std::multimap<HashType, HashType> getOutEdges(const HashType &hash) const
    {
        auto const& node = store.at(hash);
        return node.out_edges;
    }

    std::vector<Edge> getEdges(const HashType &from, const HashType &to) const
    {
        std::vector<Edge> edges;
        auto range = in_edges.equal_range(to);
        for (auto it = range.first; it != range.second; ++it)
        {
            if (it->second.from == from)
            {
                edges.push_back(it->second);
            }
        }
        return edges;
    }

    void setOOBData(const HashType &hash, const std::u32string &key, const std::any &value)
    {
        std::lock_guard<std::mutex> lock(mutex);
        oob_data[hashToU32string(hash) + U":" + key] = value;
    }

    static std::u32string hashToU32string(const HashType &hash)
    {
        std::u32string res;
        size_t pos = 0;
        const char32_t hex_chars[] = U"0123456789ABCDEF";
        for (pos = 0; pos < hash.size(); pos++) {
            res += hex_chars[(hash[pos] >> 4) & 0x0F];
            res += hex_chars[hash[pos] & 0x0F];
        }
        return res;
    }

    static std::string hashToString(const HashType &hash)
    {
        std::string res;
        size_t pos = 0;
        const char hex_chars[] = "0123456789ABCDEF";
        for (pos = 0; pos < hash.size(); pos++) {
            res += hex_chars[(hash[pos] >> 4) & 0x0F];
            res += hex_chars[hash[pos] & 0x0F];
        }
        return res;
    }

    static HashType stringToHash(const std::string &str)
    {
        HashType hash;
        const char hex_chars[] = "0123456789ABCDEF";
        size_t pos = 0;
        for (pos = 0; pos < hash.size(); pos++) {
            hash[pos] = (hex_chars[str[pos * 2]] << 4) | hex_chars[str[pos * 2 + 1]];
        }
        return hash;
    }

    HashType typeFromU32string(const std::u32string &str, std::optional<HashType> type_of_type);
private:
    NodeStore() = default;
    std::map<HashType, Node> store;
    std::unordered_multimap<HashType, Edge> in_edges;
    std::map<std::u32string, std::any> oob_data;
    std::map<std::u32string, HashType> type_map;

    mutable std::mutex mutex;
};




} // namespace lddk
