#include "node.h"

nlohmann::json lddk::Node::to_json() const
{
	nlohmann::json j;
	if (isWriteable()) return j;
	std::optional<HashType> type_hash        = getTypeHash();
	if (type_hash.has_value()) j["type"] = base64_encode(getTypeHash().value());
	j["hash"]        = lddk::NodeStore::hashToString(getHash().value());
	size_t data_size = getDataSize();
	if (data_size > 0) j["data"] = base64_encode(getData(), data_size);

	for (auto const& [key, value] : out_edges) {
		j["out_edges"][lddk::NodeStore::hashToString(key)] = lddk::NodeStore::hashToString(value);
	}

	return j;
}
lddk::Node lddk::Node::from_json(const nlohmann::json &j)
{
	Node node;
	node.type_hash        = base64_decode(j["type"].get<std::string>());
	node.hash             = lddk::NodeStore::stringToHash(j["hash"].get<std::string>());
	node.data             = base64_decode(j["data"].get<std::string>(), node.data_size);
	for (auto const& [key, value] : j["out_edges"].items()) {
		node.out_edges.insert({lddk::NodeStore::stringToHash(key), lddk::NodeStore::stringToHash(value.get<std::string>())});
	}
	return node;
}
std::string lddk::Node::base64_encode(const void *data, size_t data_size)
{
	static const std::string base64_chars
		= "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
	std::string res;
	for (size_t i = 0; i < data_size; i += 3)
	{
		uint32_t val               = 0;
		size_t   current_data_size = std::min(data_size - i, size_t(3));
		for (size_t j = 0; j < current_data_size; j++)
		{
			val |= static_cast<const uint8_t *>(data)[i + j] << (8 * (2 - j));
		}
		for (size_t j = 0; j < 4 - (3 - current_data_size); j++)
		{
			res += base64_chars[(val >> (6 * (3 - j))) & 0x3F];
		}
		if (current_data_size < 3)
		{
			for (size_t j = 0; j < 3 - current_data_size; j++)
			{
				res += '=';
			}
		}
	}
	return res;
}
void *lddk::Node::base64_decode(const std::string &base64_str, size_t &data_size)
{
	static const std::string base64_chars
		= "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
	size_t padding = 0;
	if (base64_str.size() % 4 != 0) { return nullptr; }
	if (base64_str.size() > 0 && base64_str[base64_str.size() - 1] == '=') { padding++; }
	if (base64_str.size() > 1 && base64_str[base64_str.size() - 2] == '=') { padding++; }
	if (base64_str.size() > 2 && base64_str[base64_str.size() - 3] == '=') { padding++; }
	data_size  = (base64_str.size() * 3 - padding) / 4;
	void *data = malloc(data_size);
	for (size_t i = 0; i < base64_str.size(); i += 4)
	{
		uint32_t val = 0;
		for (size_t j = 0; j < 4; j++)
		{
			if (base64_str[i + j] == '=') { break; }
			size_t pos = base64_chars.find(base64_str[i + j]);
			assert(pos != std::string::npos);
			val |= pos << (6 * (3 - j));
		}
		for (size_t j = 0; j < 3 - (4 - (base64_str.size() - i)); j++)
		{
			static_cast<uint8_t *>(data)[i / 4 * 3 + j] = (val >> (8 * (2 - j))) & 0xFF;
		}
	}
	return data;
}
std::string lddk::Node::base64_encode(const HashType &hash)
{
	return base64_encode(hash.data(), hash.size());
}
lddk::HashType lddk::Node::base64_decode(const std::string &base64_str)
{
	size_t data_size;
	void  *data = base64_decode(base64_str, data_size);
	assert(data_size == BLAKE3_HASH_SIZE);
	HashType hash;
	memcpy(hash.data(), data, data_size);
	free(data);
	return hash;
}
lddk::HashType lddk::Node::finalize()
{
	const Node *old_node = NodeStore::instance().retrieve(hash.value());
	if (old_node && !old_node->isEqual(*this, true)) { exit(1); }
	hash = blake3_hash(data,
					   data_size,
					   type_hash.value_or(HashType {}),
					   out_edges);
	return *hash;
}

lddk::HashType lddk::Node::blake3_hash(void           *data,
									   size_t          data_size,
									   const HashType &type_hash,
                                       const std::multimap<HashType, HashType> &out_edges)
{
	blake3_hasher hasher;
	blake3_hasher_init(&hasher);
	blake3_hasher_update(&hasher, type_hash.data(), type_hash.size());
	for (auto const& [key, value] : out_edges) {
		blake3_hasher_update(&hasher, key.data(), key.size());
		blake3_hasher_update(&hasher, value.data(), value.size());
	}
	blake3_hasher_update(&hasher, data, data_size);
	HashType hash;
	blake3_hasher_finalize(&hasher, hash.data(), hash.size());
	return hash;
}
const lddk::Node *lddk::NodeStore::commitObject(Node &node)
{
	std::lock_guard<std::mutex> lock(mutex);
	HashType                    hash       = node.finalize();
	auto                        current_it = store.find(hash);
	if (current_it != store.end())
	{
		return nullptr;
	} else {
		store[hash] = node;
		for (auto const& [key, value] : node.out_edges) {
			in_edges.insert({value, {hash, key, value}});
		}
	}
	return &store[hash];
}
const lddk::Node *lddk::NodeStore::retrieve(const HashType &hash) const
{
	std::lock_guard<std::mutex> lock(mutex);
	auto                        it = store.find(hash);
	if (it == store.end()) return nullptr;
	return &it->second;
}
lddk::NodeStore &lddk::NodeStore::instance()
{
	static NodeStore store;
	return store;
}

lddk::HashType lddk::NodeStore::typeFromU32string(const std::u32string &str, std::optional<HashType> type_of_type)
{
	auto node = type_map.find(str);
	static HashType default_type = typeFromU32string(U"Type",{});
	if (node != type_map.end()) {
		return node->second;
	} else {
		size_t data_size = sizeof(uint32_t) * str.size();
		uint8_t *data = (uint8_t *)malloc(data_size);
		memcpy(data, str.data(), data_size);
		Node node;
		node.updateData(data, data_size);
		node.type_hash = type_of_type.value_or(default_type);
		commitObject(node);
		type_map[str] = node.getHash().value();
		return node.getHash().value();
	}
}

