## Lexer问题修复

### 语法支持完善



## Parser整体设计

### 基础数据类型设计

由于要支持基于内容寻址，以及可复现构建，基础数据类型需要能被hash到一个稳定的索引上，考虑在AST的数据结构上就实现这一点，以同像性为重，未来类型系统设计考虑复用这个数据格式。从这个需求来看CoW似乎是有必要实现的。

目前调研了一些JSON/BSON库，都不好做稳定hash，并且CoW支持不好。

整理了一些要点：
1. 以类似BSON小文档的形式组织，称之为节点
2. 能对文档实现稳定的哈希算法用于内容寻址
3. 支持CoW
    * 节点可能链接到其它节点上，组成DAG
    * 需要支持引用计数
    * 需要探讨如何实现节点引用的序列化，似乎应该直接用哈希值
4. 支持序列化和反序列化
5. 提供类似nlohmann/json.hpp的接口用于方便的构建和访问
6. 单头文件形式，文件名未定

### Parser状态设计

待定，考虑参考分词器的设计

## 整体要求

保持简洁，尽量单文件内完成所需的逻辑。
<!-- 测试用例编译方法：运行windows_build.bat
运行测试方法：`."D:/git/LDDK/build/Debug/lexer_test.exe"` -->
测试用例编译方法：`./build.sh`
运行测试方法：`./build/lexer_test`（可以传入测试文件的相对路径，相对于工作目录）
编译并测试：`./build.sh && ./build/lexer_test`