block test_keywords_basic {
    block input : _lexer_input!
    ========
    var const fn if else while for match true false namespace type block
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    var
    +Keyword+
    const
    +Keyword+
    fn
    +Keyword+
    if
    +Keyword+
    else
    +Keyword+
    while
    +Keyword+
    for
    +Keyword+
    match
    +Keyword+
    true
    +Keyword+
    false
    +Keyword+
    namespace
    +Keyword+
    type
    +Keyword+
    block
    ========
}

block test_keywords_mixed_with_identifiers {
    block input : _lexer_input!
    ========
    var myVar const myConst fn myFunction
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    var
    +Identifier+
    myVar
    +Keyword+
    const
    +Identifier+
    myConst
    +Keyword+
    fn
    +Identifier+
    myFunction
    ========
}

block test_keywords_in_context {
    block input : _lexer_input!
    ========
    fn main() {
        var x = 42;
        if (x > 0) {
            return true;
        } else {
            return false;
        }
    }
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    fn
    +Identifier+
    main
    +FunctionCall+
    (
    +RightParen+
    )
    +LeftBrace+
    {
    +Keyword+
    var
    +Identifier+
    x
    +BinaryOperator+
    =
    +Number+
    42
    +StatementTerminator+
    ;
    +Keyword+
    if
    +LeftParen+
    (
    +Identifier+
    x
    +BinaryOperator+
    >
    +Number+
    0
    +RightParen+
    )
    +LeftBrace+
    {
    +Identifier+
    return
    +Keyword+
    true
    +StatementTerminator+
    ;
    +RightBrace+
    }
    +Keyword+
    else
    +LeftBrace+
    {
    +Identifier+
    return
    +Keyword+
    false
    +StatementTerminator+
    ;
    +RightBrace+
    }
    +RightBrace+
    }
    ========
}

block test_non_keywords {
    block input : _lexer_input!
    ========
    variable constant function ifelse whileloop
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    variable
    +Identifier+
    constant
    +Identifier+
    function
    +Identifier+
    ifelse
    +Identifier+
    whileloop
    ========
}
