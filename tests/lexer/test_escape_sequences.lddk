block test_string_basic_escapes {
    block input : _lexer_input !
    ========
    "hello\nworld"
    ========

    block result : _lexer_expected !
    ========
    +String+
    hello\nworld
    ========
}

block test_string_all_escapes {
    block input : _lexer_input !
    ========
    "\n\t\r\\\'\"\b\f\a\v\0"
    ========

    block result : _lexer_expected !
    ========
    +String+
    \n\t\r\\\'\"\b\f\a\v\0
    ========
}

block test_char_basic_escapes {
    block input : _lexer_input !
    ========
    '\n' '\t' '\r' '\\' '\'' '\"'
    ========

    block result : _lexer_expected !
    ========
    +Char+
    \u000A
    +Char+
    \u0009
    +Char+
    \u000D
    +Char+
    \u005C
    +Char+
    \u0027
    +Char+
    \u0022
    ========
}

block test_char_special_escapes {
    block input : _lexer_input !
    ========
    '\b' '\f' '\a' '\v' '\0'
    ========

    block result : _lexer_expected !
    ========
    +Char+
    \u0008
    +Char+
    \u000C
    +Char+
    \u0007
    +Char+
    \u000B
    +Char+
    \u0000
    ========
}

block test_invalid_escape_string {
    block input : _lexer_input !
    ========
    "hello\xworld"
    ========

    block error : _lexer_error_state !
    ========
    1:8
    invalid_escape_sequence
    ========
}

block test_invalid_escape_char {
    block input : _lexer_input !
    ========
    '\x'
    ========

    block error : _lexer_error_state !
    ========
    1:3
    invalid_escape_sequence
    ========
}

block test_unterminated_string {
    block input : _lexer_input !
    ========
    "hello world
    ========

    block error : _lexer_error_state !
    ========
    1:13
    not_in_normal_state_after_finalization
    ========
}

block test_unterminated_char {
    block input : _lexer_input !
    ========
    'a
    ========

    block error : _lexer_error_state !
    ========
    1:3
    char_must_be_one_character
    ========
}

block test_empty_char {
    block input : _lexer_input !
    ========
    ''
    ========

    block result : _lexer_expected !
    ========
    +Char+
    
    
    ========
}

block test_multi_char_error {
    block input : _lexer_input !
    ========
    'ab'
    ========

    block error : _lexer_error_state !
    ========
    1:3
    char_must_be_one_character
    ========
}
