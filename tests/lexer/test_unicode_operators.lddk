block test_unicode_math_symbols {
    block input : _lexer_input!
    ========
    a ≤ b ≥ c ≠ d ≈ e
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    ≤
    +Identifier+
    b
    +BinaryOperator+
    ≥
    +Identifier+
    c
    +BinaryOperator+
    ≠
    +Identifier+
    d
    +BinaryOperator+
    ≈
    +Identifier+
    e
    ========
}

block test_unicode_currency_symbols {
    block input : _lexer_input!
    ========
    a € b ¥ c £ d()[1]
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    €
    +Identifier+
    b
    +BinaryOperator+
    ¥
    +Identifier+
    c
    +BinaryOperator+
    £
    +Identifier+
    d
    +FunctionCall+
    (
    +RightParen+
    )
    +IndexAccess+
    [
    +Number+
    1
    +RightBracket+
    ]
    ========
}

block test_unicode_punctuation {
    block input : _lexer_input!
    ========
    a § b ¶ c † d
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    §
    +Identifier+
    b
    +BinaryOperator+
    ¶
    +Identifier+
    c
    +BinaryOperator+
    †
    +Identifier+
    d
    ========
}

block test_unicode_dash_punctuation {
    block input : _lexer_input!
    ========
    a — b – c
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    —
    +Identifier+
    b
    +BinaryOperator+
    –
    +Identifier+
    c
    ========
}

block test_excluded_ascii_operators {
    block input : _lexer_input!
    ========
    a . b - c * d / e % a.b[3].d
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    .
    +Identifier+
    b
    +BinaryOperator+
    -
    +Identifier+
    c
    +BinaryOperator+
    *
    +Identifier+
    d
    +BinaryOperator+
    /
    +Identifier+
    e
    +BinaryOperator+
    %
    +Identifier+
    a
    +MemberAccess+
    .
    +Identifier+
    b
    +IndexAccess+
    [
    +Number+
    3
    +RightBracket+
    ]
    +MemberAccess+
    .
    +Identifier+
    d
    ========
}

block test_mixed_unicode_ascii {
    block input : _lexer_input!
    ========
    a + !(b ≤ c)# * d ≠ e
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    +
    +PrefixOperator+
    !
    +LeftParen+
    (
    +Identifier+
    b
    +BinaryOperator+
    ≤
    +Identifier+
    c
    +RightParen+
    )
    +SuffixOperator+
    #
    +BinaryOperator+
    *
    +Identifier+
    d
    +BinaryOperator+
    ≠
    +Identifier+
    e
    ========
}
