block test_enhanced_block_basic {
    block input : _lexer_input!
    ========
    block content : mytype!
    ---
    Hello World
    ---
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    block
    +Identifier+
    content
    +BinaryOperator+
    :
    +Identifier+
    mytype
    +BlockContent+
    Hello World
    ========
}

block test_enhanced_block_equals {
    block input : _lexer_input!
    ========
    block content : mytype!
    ===
    Hello World
    ===
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    block
    +Identifier+
    content
    +BinaryOperator+
    :
    +Identifier+
    mytype
    +BlockContent+
    Hello World
    ========
}

block test_enhanced_block_minimum_delimiter {
    block input : _lexer_input!
    ========
    block content : mytype!
    ---
    Hello
    ---
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    block
    +Identifier+
    content
    +BinaryOperator+
    :
    +Identifier+
    mytype
    +BlockContent+
    Hello
    ========
}

block test_enhanced_block_long_delimiter {
    block input : _lexer_input!
    ========
    block content : mytype!
    =====================================
    Hello World
    =====================================
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    block
    +Identifier+
    content
    +BinaryOperator+
    :
    +Identifier+
    mytype
    +BlockContent+
    Hello World
    ========
}

block test_enhanced_block_multiline {
    block input : _lexer_input!
    ========
    block content : mytype!
    ---
    Line 1
    Line 2
    Line 3
    ---
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    block
    +Identifier+
    content
    +BinaryOperator+
    :
    +Identifier+
    mytype
    +BlockContent+
    Line 1
    Line 2
    Line 3
    ========
}

block test_enhanced_block_with_indentation {
    block input : _lexer_input!
    ========
      block content : mytype!
      ---
      Hello World
      ---
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    block
    +Identifier+
    content
    +BinaryOperator+
    :
    +Identifier+
    mytype
    +BlockContent+
    Hello World
    ========
}

block test_enhanced_block_empty {
    block input : _lexer_input!
    ========
    block content : mytype!
    ---
    ---
    ========

    block result : _lexer_expected!
    ========
    +Keyword+
    block
    +Identifier+
    content
    +BinaryOperator+
    :
    +Identifier+
    mytype
    +BlockContent+
    
    ========
}
