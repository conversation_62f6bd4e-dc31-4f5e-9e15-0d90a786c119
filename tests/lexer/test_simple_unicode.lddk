block test_simple_unicode {
    block input : _lexer_input!
    ========
    a @ b
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    @
    +Identifier+
    b
    ========
}

block test_simple_ascii_operators {
    block input : _lexer_input!
    ========
    a $ b
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    $
    +Identifier+
    b
    ========
}
