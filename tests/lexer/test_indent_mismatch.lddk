
block test_indent_2 {
block input : _lexer_input!
=========================
block a:a !
  ========
  content with 2 spaces
  ========
=========================

block output : _lexer_expected !
=========================
+Keyword+
block
+Identifier+
a
+BinaryOperator+
:
+Identifier+
a
+BlockContent+
content with 2 spaces
=========================

}

block test_indent_4 {
block input : _lexer_input!
=========================
  block b : b!
    ========
    content with 4 spaces
    ========
=========================

block output : _lexer_expected !
=========================
+Keyword+
block
+Identifier+
b
+BinaryOperator+
:
+Identifier+
b
+BlockContent+
content with 4 spaces
=========================

}

block test_indent_bad {
block input1 : _lexer_input!
=========================
    block test3_bad : _lexer_input!
    ========
    content starts with 2 spaces
  ========
=========================

block error : _lexer_error_state !
=========================
4:3
enhanced_block_indent_mismatch
=========================

}