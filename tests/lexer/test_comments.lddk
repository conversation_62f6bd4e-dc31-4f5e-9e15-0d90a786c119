block test_line_comment_basic {
    block input : _lexer_input!
    ========
    a // this is a comment
    b
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    ========
}

block test_line_comment_end_of_file {
    block input : _lexer_input!
    ========
    a // comment at end
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    ========
}

block test_block_comment_basic {
    block input : _lexer_input!
    ========
    a /* this is a block comment */ b
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    ========
}

block test_block_comment_multiline {
    block input : _lexer_input!
    ========
    a /* this is a
    multiline
    block comment */ b
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    ========
}

block test_multiple_line_comments {
    block input : _lexer_input!
    ========
    a // first comment
    b // second comment
    c
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    +Identifier+
    c
    ========
}

block test_multiple_block_comments {
    block input : _lexer_input!
    ========
    a /* first */ b /* second */ c
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    +Identifier+
    c
    ========
}

block test_comment_with_operators_no_space {
    block input : _lexer_input!
    ========
    a + b// addition
    c */* multiplication */d/* multiplication */* e
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    +
    +Identifier+
    b
    +Identifier+
    c
    +BinaryOperator+
    *
    +Identifier+
    d
    +BinaryOperator+
    *
    +Identifier+
    e
    ========
}

block test_division_vs_comment {
    block input : _lexer_input!
    ========
    a / b
    c // comment
    d /* block */
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    /
    +Identifier+
    b
    +Identifier+
    c
    +Identifier+
    d
    ========
}
