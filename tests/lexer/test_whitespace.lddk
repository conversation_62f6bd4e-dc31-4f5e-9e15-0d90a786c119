block test_spaces {
    block input : _lexer_input!
    ========
    a   b    c
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    +Identifier+
    c
    ========
}

block test_tabs {
    block input : _lexer_input!
    ========
    a	b		c
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    +Identifier+
    c
    ========
}

block test_mixed_whitespace {
    block input : _lexer_input!
    ========
    a 	 b	  c
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    +Identifier+
    c
    ========
}

block test_newlines {
    block input : _lexer_input!
    ========
    a
    b

    c
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    +Identifier+
    c
    ========
}

block test_carriage_return {
    block input : _lexer_input!
    ========
    a b
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +Identifier+
    b
    ========
}

block test_whitespace_around_operators {
    block input : _lexer_input!
    ========
    a + b
    c*d
    e 	- 	f
    ========

    block result : _lexer_expected!
    ========
    +Identifier+
    a
    +BinaryOperator+
    +
    +Identifier+
    b
    +Identifier+
    c
    +BinaryOperator+
    *
    +Identifier+
    d
    +Identifier+
    e
    +BinaryOperator+
    -
    +Identifier+
    f
    ========
}

block test_whitespace_in_brackets {
    block input : _lexer_input!
    ========
    ( a , b )
    [ c , d ]
    { e , f }
    ========

    block result : _lexer_expected!
    ========
    +LeftParen+
    (
    +Identifier+
    a
    +BinaryOperator+
    ,
    +Identifier+
    b
    +RightParen+
    )
    +LeftBracket+
    [
    +Identifier+
    c
    +BinaryOperator+
    ,
    +Identifier+
    d
    +RightBracket+
    ]
    +LeftBrace+
    {
    +Identifier+
    e
    +BinaryOperator+
    ,
    +Identifier+
    f
    +RightBrace+
    }
    ========
}
