cmake_minimum_required(VERSION 3.20)
project(MyCompiler)

if(MSVC)
  add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
  add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
endif()


# 指定 vcpkg 工具链
if (NOT DEFINED VCPKG_ROOT)
    set(VCPKG_ROOT $ENV{VCPKG_ROOT})
endif()
set(CMAKE_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake"
    CACHE STRING "Vcpkg toolchain file")

# 检查工具链是否生效
if(DEFINED VCPKG_TARGET_TRIPLET)
    message(STATUS "Vcpkg triplet: ${VCPKG_TARGET_TRIPLET}")
else()
    message(WARNING "Vcpkg toolchain not properly loaded!")
endif()

message(STATUS "VCPKG_ROOT: ${VCPKG_ROOT}")

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 20)

# 查找 LLVM 包
find_package(LLVM REQUIRED CONFIG)
list(APPEND CMAKE_MODULE_PATH "${LLVM_CMAKE_DIR}")
message(STATUS "Found LLVM ${LLVM_PACKAGE_VERSION}")

# 查找 ICU 包
find_package(ICU REQUIRED COMPONENTS uc i18n)

# 查找 nlohmann-json 包
find_package(nlohmann_json REQUIRED)

# 查找 blake3 包
find_package(blake3 CONFIG REQUIRED)

# 添加 LLVM 配置
include_directories(${LLVM_INCLUDE_DIRS})
add_definitions(${LLVM_DEFINITIONS})
llvm_map_components_to_libnames(LLVM_LIBS core irreader)  # 按需添加组件

# 添加可执行文件
add_executable(my_compiler src/llvm-frontend/main.cpp src/llvm-frontend/node.cpp)
target_link_libraries(my_compiler PRIVATE ${LLVM_LIBS} nlohmann_json::nlohmann_json BLAKE3::blake3)

# 添加测试
add_executable(lexer_test tests/lexer/lexer_test.cpp)
target_link_libraries(lexer_test PRIVATE ${LLVM_LIBS} ICU::i18n ICU::uc)

add_executable(node_test tests/temp/node_test.cpp)
target_link_libraries(node_test PRIVATE ${LLVM_LIBS})

add_executable(ast_example tests/temp/ast_example.cpp)
target_link_libraries(ast_example PRIVATE ${LLVM_LIBS})
